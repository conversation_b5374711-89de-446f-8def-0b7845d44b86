package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.module.mentoring.command.StudentInquiriesMentoringCommand
import com.cleevio.fundedmind.application.module.mentoring.event.MentoringInquiryCreatedEvent
import com.cleevio.fundedmind.domain.mentoringinquiry.CreateMentoringInquiryService
import com.cleevio.fundedmind.domain.mentoringinquiry.MentoringInquiryAnswer
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class StudentInquiriesMentoringCommandHandler(
    private val createMentoringInquiryService: CreateMentoringInquiryService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<IdResult, StudentInquiriesMentoringCommand> {

    override val command = StudentInquiriesMentoringCommand::class

    @Transactional
    override fun handle(command: StudentInquiriesMentoringCommand): IdResult {
        val mentoringInquiry = createMentoringInquiryService.create(
            studentId = command.studentId,
            traderId = command.traderId,
            inquiryAnswers = command.inquiryAnswers.map {
                MentoringInquiryAnswer(
                    question = it.question,
                    answer = it.answer,
                )
            },
        )

        applicationEventPublisher.publishEvent(MentoringInquiryCreatedEvent(mentoringInquiryId = mentoringInquiry.id))

        return IdResult(id = mentoringInquiry.id)
    }
}
