package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.crm.port.out.PatchCrmCustomerRequest
import com.cleevio.fundedmind.application.module.gamedocument.command.ApproveGameDocumentCommand
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentHasWrongStateException
import com.cleevio.fundedmind.domain.gamelevelprogress.GameLevelProgressRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.time.LocalDate

class ApproveGameDocumentCommandHandlerTest(
    @Autowired private val underTest: ApproveGameDocumentCommandHandler,
    @Autowired private val gameDocumentRepository: GameDocumentRepository,
    @Autowired private val studentRepository: StudentRepository,
    @Autowired private val gameLevelProgressRepository: GameLevelProgressRepository,
) : IntegrationTest() {

    @Test
    fun `should approve payout and update student game level`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.FOUR)
        }

        // Create a game document to approve
        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = 6_000.toBigDecimal(),
            previousLevel = GameLevel.FOUR,
            reachedLevel = GameLevel.FIVE,
            payoutDate = LocalDate.now(),
            truthScore = 80,
            scoreMessage = "Looks fine",
        )

        every { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(any()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025 10:00:00
            ),
        )

        // Verify the approval
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
            previousLevel shouldBe GameLevel.FOUR
            reachedLevel shouldBe GameLevel.FIVE
            payoutAmount!! shouldBeEqualComparingTo 6_000.toBigDecimal()
            denyMessage shouldBe null
            deniedAt shouldBe null
        }

        // Verify student game level is updated
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.FIVE

        // Verify progress entries are created
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries shouldHaveSize 1
        progressEntries.single().run {
            gameLevel shouldBe GameLevel.FIVE
            shown shouldBe false
        }

        verify { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(1.toUUID()) }
        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.FIVE) }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.FIVE,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 6_000.toBigDecimal(),
                ),
            )
        }
    }

    @Test
    fun `should approve certificate and send certificate email`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.TWO)
        }

        // Create a game document to approve
        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.THREE,
        )

        every { sendEmailService.sendEmailGameDocumentCertificateApproved(any()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025 10:00:00
            ),
        )

        // Verify the approval
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
            previousLevel shouldBe GameLevel.TWO
            reachedLevel shouldBe GameLevel.THREE
            payoutAmount shouldBe null
            denyMessage shouldBe null
            deniedAt shouldBe null
        }

        // Verify student game level is updated
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.THREE

        // Verify progress entries are created
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries shouldHaveSize 1
        progressEntries.single().run {
            gameLevel shouldBe GameLevel.THREE
            shown shouldBe false
        }

        verify { sendEmailService.sendEmailGameDocumentCertificateApproved(1.toUUID()) }
        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.THREE) }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.THREE,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 0.toBigDecimal(),
                ),
            )
        }
    }

    @Test
    fun `should approve backtesting and send backtesting email`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.ONE)
        }

        // Create a game document to approve
        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.BACKTESTING,
            payoutAmount = null,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.TWO,
        )

        every { sendEmailService.sendEmailGameDocumentBacktestingApproved(any()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025 10:00:00
            ),
        )

        // Verify the approval
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
            previousLevel shouldBe GameLevel.ONE
            reachedLevel shouldBe GameLevel.TWO
            payoutAmount shouldBe null
            denyMessage shouldBe null
            deniedAt shouldBe null
        }

        // Verify student game level is updated
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.TWO

        // Verify progress entries are created
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries shouldHaveSize 1
        progressEntries.single().run {
            gameLevel shouldBe GameLevel.TWO
            shown shouldBe false
        }

        verify { sendEmailService.sendEmailGameDocumentBacktestingApproved(1.toUUID()) }
        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.TWO) }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.TWO,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 0.toBigDecimal(),
                ),
            )
        }
    }

    @Test
    fun `should approve game document with multiple level jumps`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 111.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.THREE)
        }

        // Create a game document to approve with multiple level jump
        val gameDocument = dataHelper.getGameDocument(
            id = 302.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 36_000.toBigDecimal(),
            previousLevel = GameLevel.THREE,
            reachedLevel = GameLevel.SEVEN, // Jump from 3 to 7
            payoutDate = LocalDate.now(),
            truthScore = 90,
            scoreMessage = "Great progress",
        )

        every { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(any()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(),
            ),
        )

        // Verify student game level is updated to the highest level
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.SEVEN

        // Verify progress entries are created for all gained levels
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries.map { it.gameLevel } shouldContainExactlyInAnyOrder
            listOf(GameLevel.FOUR, GameLevel.FIVE, GameLevel.SIX, GameLevel.SEVEN)

        verify { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(302.toUUID()) }
        verify { sendEmailService.sendEmailGameLevelGained(111.toUUID(), GameLevel.FOUR) }
        verify { sendEmailService.sendEmailGameLevelGained(111.toUUID(), GameLevel.FIVE) }
        verify { sendEmailService.sendEmailGameLevelGained(111.toUUID(), GameLevel.SIX) }
        verify { sendEmailService.sendEmailGameLevelGained(111.toUUID(), GameLevel.SEVEN) }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.SEVEN,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 36_000.toBigDecimal(),
                ),
            )
        }
    }

    @Test
    fun `should not create progress entries when current level equals reached level`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 112.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.NINE)
        }

        // Create a game document to approve with same level
        val gameDocument = dataHelper.getGameDocument(
            id = 303.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 5_000.toBigDecimal(),
            previousLevel = GameLevel.NINE,
            reachedLevel = GameLevel.NINE, // Same level
            payoutDate = LocalDate.now(),
            truthScore = 70,
            scoreMessage = "Maintaining level",
        )

        every { sendEmailService.sendEmailGameDocumentPayoutApproved(any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(),
            ),
        )

        // Verify student game level remains the same
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.NINE

        // Verify no progress entries are created
        gameLevelProgressRepository.findAllByStudentId(user.id).shouldBeEmpty()

        verify { sendEmailService.sendEmailGameDocumentPayoutApproved(any()) }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.NINE,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 5_000.toBigDecimal(),
                ),
            )
        }
    }

    @Test
    fun `should throw exception when game document is not in WAITING state`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 113.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document that is already approved
        val gameDocument = dataHelper.getGameDocument(
            id = 304.toUUID(),
            studentId = user.id,
            entityModifier = { it.approveAwaiting() },
        )

        // Try to approve the already approved document
        shouldThrow<GameDocumentHasWrongStateException> {
            underTest.handle(
                ApproveGameDocumentCommand(
                    gameDocumentId = gameDocument.id,
                    now = "2025-09-15T10:00:00Z".toInstant(),
                ),
            )
        }
    }

    @Test
    fun `should approve previously denied document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.THREE,
            )
        }

        // Create a game document that is denied
        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 16_000.toBigDecimal(),
            previousLevel = GameLevel.THREE,
            reachedLevel = GameLevel.SIX,
            entityModifier = {
                it.denyAwaiting(
                    message = "Not enough evidence",
                    now = "2025-09-10T10:00:00Z".toInstant(), // 10.9.2025
                )
            },
        )

        every { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(any()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the previously denied document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the approval
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            payoutAmount!! shouldBeEqualComparingTo 16_000.toBigDecimal()
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant() // 15.9.2025
            previousLevel shouldBe GameLevel.THREE
            reachedLevel shouldBe GameLevel.SIX
            denyMessage shouldBe "Not enough evidence"
            deniedAt shouldBeAbout "2025-09-10T10:00:00Z".toInstant() // 10.9.2025
        }

        // Verify student game level is updated
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.SIX

        // Verify progress entries are created
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries shouldHaveSize 3
        progressEntries.map { it.gameLevel } shouldContainExactlyInAnyOrder
            listOf(GameLevel.FOUR, GameLevel.FIVE, GameLevel.SIX)

        verify { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(1.toUUID()) }
        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.FOUR) }
        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.FIVE) }
        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.SIX) }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.SIX,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 16_000.toBigDecimal(),
                ),
            )
        }
    }

    @Test
    fun `should approve previously denied document when student level has increased since denial`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.FOUR,
            )
        }

        // Create a game document that was denied when student was at level 3 and it would move them to level 6
        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 33_000.toBigDecimal(),
            previousLevel = GameLevel.THREE,
            reachedLevel = GameLevel.SIX,
            entityModifier = {
                it.denyAwaiting(
                    message = "Needs more details",
                    now = "2025-09-05T10:00:00Z".toInstant(), // 5.9.2025
                )
            },
        )

        // doc that was approved and moved student level 3 -> 4
        val approvedDoc = dataHelper.getGameDocument(
            id = 2.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 3_000.toBigDecimal(),
            previousLevel = GameLevel.THREE,
            reachedLevel = GameLevel.FOUR,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(any()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the previously denied document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(),
            ),
        )

        // Verify the approval with updated levels
        val documents = gameDocumentRepository.findAll()
        documents.size shouldBe 2
        documents.single { it.id == approvedDoc.id }!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            payoutAmount!! shouldBeEqualComparingTo 3_000.toBigDecimal()
            approvedAt shouldBeAbout "2025-09-10T10:00:00Z".toInstant() // 10.9.2025
            previousLevel shouldBe GameLevel.THREE
            reachedLevel shouldBe GameLevel.FOUR
        }
        documents.single { it.id == gameDocument.id }!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            payoutAmount!! shouldBeEqualComparingTo 33_000.toBigDecimal()
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant() // 15.9.2025
            previousLevel shouldBe GameLevel.FOUR // Updated from student's current level
            reachedLevel shouldBe GameLevel.SEVEN // 3k + 33k = 36k, (5k + 10k + 20k is threshold for level 7)
            denyMessage shouldBe "Needs more details"
            deniedAt shouldBeAbout "2025-09-05T10:00:00Z".toInstant() // 5.9.2025
        }

        // Verify student game level is updated
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.SEVEN

        // Verify progress entries are created for the new level
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries.map { it.gameLevel } shouldContainExactlyInAnyOrder
            listOf(GameLevel.FIVE, GameLevel.SIX, GameLevel.SEVEN)

        verify { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(1.toUUID()) }
        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.FIVE) }
        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.SIX) }
        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.SEVEN) }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.SEVEN,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 36_000.toBigDecimal(),
                ),
            )
        }
    }

    @Test
    fun `should approve previously denied document and recompute waiting documents`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 203.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.TWO,
            )
        }

        // Create a denied game document
        val deniedDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 14_000.toBigDecimal(),
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.FIVE,
            entityModifier = { it.denyAwaiting("Incomplete information", "2025-09-01T10:00:00Z".toInstant()) },
        )

        // Create a waiting game document
        val waitingDocument = dataHelper.getGameDocument(
            id = 2.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = 2_000.toBigDecimal(),
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.FOUR,
            scoreMessage = "Excellent progress",
        )

        every { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(any()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the previously denied document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = deniedDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(),
            ),
        )

        // Verify the denied document is now approved
        gameDocumentRepository.findByIdOrNull(deniedDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            payoutAmount!! shouldBeEqualComparingTo 14_000.toBigDecimal()
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
            previousLevel shouldBe GameLevel.TWO
            reachedLevel shouldBe GameLevel.SIX // With new system, 14k is enough for level SIX (requires 10k)
            denyMessage shouldBe "Incomplete information"
            deniedAt shouldBeAbout "2025-09-01T10:00:00Z".toInstant()
        }
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.SIX

        // Verify the waiting document has been recomputed
        gameDocumentRepository.findByIdOrNull(waitingDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.WAITING
            payoutAmount!! shouldBeEqualComparingTo 2_000.toBigDecimal()
            approvedAt shouldBe null
            previousLevel shouldBe GameLevel.SIX // With new system, previousLevel is the student's current level
            reachedLevel shouldBe GameLevel.SIX // With new system, 2k + 14k = 16k is still level SIX (SEVEN is 20k)
            denyMessage shouldBe null
            deniedAt shouldBe null
        }

        // Verify progress entries are created
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries.map { it.gameLevel } shouldContainExactlyInAnyOrder
            listOf(GameLevel.THREE, GameLevel.FOUR, GameLevel.FIVE, GameLevel.SIX)

        verify { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(1.toUUID()) }
        verify { sendEmailService.sendEmailGameLevelGained(203.toUUID(), GameLevel.THREE) }
        verify { sendEmailService.sendEmailGameLevelGained(203.toUUID(), GameLevel.FOUR) }
        verify { sendEmailService.sendEmailGameLevelGained(203.toUUID(), GameLevel.FIVE) }
        verify {
            sendEmailService.sendEmailGameLevelGained(203.toUUID(), GameLevel.SIX)
        } // With new system, email is sent for level SIX as well
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.SIX,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 14_000.toBigDecimal(),
                ),
            )
        }
    }

    @Test
    fun `should approve previously denied backtesting and not change level of student`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 203.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.FIVE,
            )
        }

        // Create a denied backtesting
        val deniedDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.BACKTESTING,
            payoutAmount = null,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.TWO,
            entityModifier = {
                it.denyAwaiting(
                    message = "Incomplete information",
                    now = "2025-09-01T10:00:00Z".toInstant(), // 1.9.2025
                )
            },
        )

        // Create an approved payout that moves student to level 5
        val approvedDocument = dataHelper.getGameDocument(
            id = 2.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = 6_000.toBigDecimal(),
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.FIVE,
            scoreMessage = "Excellent progress",
            entityModifier = { it.approveAwaiting("2025-09-15T10:00:00Z".toInstant()) }, // 15.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentBacktestingApproved(any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the previously denied document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = deniedDocument.id,
                now = "2025-09-16T10:00:00Z".toInstant(), // 16.9.2025
            ),
        )

        // Verify the approval
        val documents = gameDocumentRepository.findAll()
        documents.size shouldBe 2
        documents.single { it.id == approvedDocument.id }!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant() // 15.9.2025
            payoutAmount!! shouldBeEqualComparingTo 6_000.toBigDecimal()
            previousLevel shouldBe GameLevel.ONE
            reachedLevel shouldBe GameLevel.FIVE
            denyMessage shouldBe null
            deniedAt shouldBe null
        }
        documents.single { it.id == deniedDocument.id }!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            approvedAt shouldBeAbout "2025-09-16T10:00:00Z".toInstant() // 16.9.2025
            // changed from GameLevel.ONE to student current level
            previousLevel shouldBe GameLevel.FIVE
            // changed from GameLevel.TWO to FIVE because approving document did not change level of student
            reachedLevel shouldBe GameLevel.FIVE
            denyMessage shouldBe "Incomplete information"
            deniedAt shouldBeAbout "2025-09-01T10:00:00Z".toInstant() // 1.9.2025
        }

        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.FIVE

        // Verify no progress entries are created
        gameLevelProgressRepository.findAllByStudentId(user.id).shouldBeEmpty()

        verify { sendEmailService.sendEmailGameDocumentBacktestingApproved(1.toUUID()) }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.FIVE,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 6_000.toBigDecimal(),
                ),
            )
        }
    }

    @Test
    fun `should approve waiting document and recompute waiting documents`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 203.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.TWO,
            )
        }

        // Create a waiting game documents
        val waitingDocument1 = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 14_000.toBigDecimal(),
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.SIX,
        )

        val waitingDocument2 = dataHelper.getGameDocument(
            id = 2.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = 2_000.toBigDecimal(),
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.FOUR,
            scoreMessage = "Excellent progress",
        )

        every { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(any()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Approve the previously denied document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = waitingDocument1.id,
                now = "2025-09-15T10:00:00Z".toInstant(),
            ),
        )

        // Verify the waiting document is now approved
        gameDocumentRepository.findByIdOrNull(waitingDocument1.id)!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            payoutAmount!! shouldBeEqualComparingTo 14_000.toBigDecimal()
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
            previousLevel shouldBe GameLevel.TWO
            reachedLevel shouldBe GameLevel.SIX // With new system, 14k is enough for level SIX (requires 10k)
            denyMessage shouldBe null
            deniedAt shouldBeAbout null
        }
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.SIX

        // Verify the waiting document has been recomputed
        gameDocumentRepository.findByIdOrNull(waitingDocument2.id)!!.run {
            state shouldBe GameDocumentApprovalState.WAITING
            payoutAmount!! shouldBeEqualComparingTo 2_000.toBigDecimal()
            approvedAt shouldBe null
            previousLevel shouldBe GameLevel.SIX // With new system, previousLevel is the student's current level
            reachedLevel shouldBe GameLevel.SIX // With new system, 2k + 14k = 16k is still level SIX (SEVEN is 20k)
            denyMessage shouldBe null
            deniedAt shouldBe null
        }

        // Verify progress entries are created
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries.map { it.gameLevel } shouldContainExactlyInAnyOrder
            listOf(GameLevel.THREE, GameLevel.FOUR, GameLevel.FIVE, GameLevel.SIX)

        verify { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(1.toUUID()) }
        verify { sendEmailService.sendEmailGameLevelGained(203.toUUID(), GameLevel.THREE) }
        verify { sendEmailService.sendEmailGameLevelGained(203.toUUID(), GameLevel.FOUR) }
        verify { sendEmailService.sendEmailGameLevelGained(203.toUUID(), GameLevel.FIVE) }
        verify {
            sendEmailService.sendEmailGameLevelGained(203.toUUID(), GameLevel.SIX)
        } // With new system, email is sent for level SIX as well
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = GameLevel.SIX,
                    totalPayout = null,
                ),
            )
        }
        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = user.hubspotIdentifier,
                    gameLevel = null,
                    totalPayout = 14_000.toBigDecimal(),
                ),
            )
        }
    }
}
