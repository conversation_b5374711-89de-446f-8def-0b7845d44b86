package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.command.UpdateCourseCommand
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderNotFoundException
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.exception.EntityIsDeletedException
import com.cleevio.fundedmind.domain.course.CourseRepository
import com.cleevio.fundedmind.domain.course.exception.CourseHomepageRequiresPublicException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class UpdateCourseCommandHandlerTest(
    @Autowired private val underTest: UpdateCourseCommandHandler,
    @Autowired private val courseRepository: CourseRepository,
) : IntegrationTest() {

    @Test
    fun `should update course`() {
        // given
        dataHelper.getTrader(2.toUUID())

        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
        )

        // when
        underTest.handle(
            UpdateCourseCommand(
                courseId = 1.toUUID(),
                traderId = 2.toUUID(),
                title = "New Course",
                courseCategory = CourseCategory.EXCLUSIVE,
                visibleToTiers = listOf(StudentTier.EXCLUSIVE),
                visibleToDiscordUsers = false,
                description = "New Description",
                color = Color.PURPLE,
                thumbnailUrl = "new thumbnailUrl",
                thumbnailAnimationUrl = "new thumbnailAnimationUrl",
                trailerUrl = "new trailerUrl",
                public = false,
                homepage = false,
            ),
        )

        val course = courseRepository.findByIdOrNull(1.toUUID())!!

        // then
        course.run {
            title shouldBe "New Course"
            courseCategory shouldBe CourseCategory.EXCLUSIVE
            visibleToTiers shouldBe listOf(StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe false
            description shouldBe "New Description"
            color shouldBe Color.PURPLE
            thumbnailUrl shouldBe "new thumbnailUrl"
            thumbnailAnimationUrl shouldBe "new thumbnailAnimationUrl"
            trailerUrl shouldBe "new trailerUrl"
            public shouldBe false
            homepage shouldBe false
        }
    }

    @Test
    fun `should throw if course is deleted`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = { it.softDelete() },
        )

        shouldThrow<EntityIsDeletedException> {
            underTest.handle(
                defaultCommand(
                    courseId = 1.toUUID(),
                    traderId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if trader does not exist`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
        )

        shouldThrow<TraderNotFoundException> {
            underTest.handle(
                defaultCommand(
                    courseId = 1.toUUID(),
                    traderId = 999.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if on homepage but not public`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
        )

        shouldThrow<CourseHomepageRequiresPublicException> {
            underTest.handle(
                defaultCommand(
                    courseId = 1.toUUID(),
                    traderId = 1.toUUID(),
                    public = false,
                    homepage = true,
                ),
            )
        }
    }

    private fun defaultCommand(
        courseId: UUID,
        traderId: UUID,
        title: String = "Course",
        courseCategory: CourseCategory = CourseCategory.TRADING_BASICS,
        visibleToTiers: List<StudentTier> = listOf(StudentTier.MASTERCLASS),
        visibleToDiscordUsers: Boolean = true,
        description: String = "Description",
        color: Color = Color.GREEN,
        thumbnailUrl: String = "thumbnailUrl",
        thumbnailAnimationUrl: String = "thumbnailAnimationUrl",
        trailerUrl: String = "trailerUrl",
        public: Boolean = true,
        homepage: Boolean = true,
    ) = UpdateCourseCommand(
        courseId = courseId,
        traderId = traderId,
        title = title,
        courseCategory = courseCategory,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        description = description,
        color = color,
        thumbnailUrl = thumbnailUrl,
        thumbnailAnimationUrl = thumbnailAnimationUrl,
        trailerUrl = trailerUrl,
        public = public,
        homepage = homepage,
    )
}
