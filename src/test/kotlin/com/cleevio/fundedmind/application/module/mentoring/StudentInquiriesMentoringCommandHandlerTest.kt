package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoring.command.StudentInquiriesMentoringCommand
import com.cleevio.fundedmind.application.module.mentoring.command.StudentInquiriesMentoringCommand.MentoringInquiryAnswerInput
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.mentoringinquiry.MentoringInquiryRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class StudentInquiriesMentoringCommandHandlerTest(
    @Autowired private val underTest: StudentInquiriesMentoringCommandHandler,
    @Autowired private val mentoringInquiryRepository: MentoringInquiryRepository,
) : IntegrationTest() {

    @Test
    fun `should create mentoring inquiry`() {
        // Create a student
        val student = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Doe",
                phone = "+************",
                studentTier = StudentTier.MASTERCLASS,
            )
        }

        // Create a trader
        val trader = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.TRADER).also {
            dataHelper.getTrader(id = it.id, firstName = "Trader", lastName = "Mentor")
        }

        every { zapierService.newMentoringInquiry(any(), any(), any(), any(), any()) } just Runs

        // When
        val result = underTest.handle(
            defaultCommand(
                studentId = student.id,
                traderId = trader.id,
                inquiryAnswers = listOf(
                    MentoringInquiryAnswerInput("Q1", "A1"),
                    MentoringInquiryAnswerInput("Q2", "A2"),
                ),
            ),
        )

        // Then
        mentoringInquiryRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe student.id
            traderId shouldBe trader.id
            answers.size shouldBe 2
            answers[0].run {
                question shouldBe "Q1"
                answer shouldBe "A1"
            }
            answers[1].run {
                question shouldBe "Q2"
                answer shouldBe "A2"
            }
        }

        verify {
            zapierService.newMentoringInquiry(
                studentName = "John Doe",
                studentEmail = "<EMAIL>",
                studentPhone = "+************",
                mentorName = "Trader Mentor",
                formattedAnswers = "Q1: A1; Q2: A2",
            )
        }
    }

    private fun defaultCommand(
        studentId: UUID,
        traderId: UUID,
        inquiryAnswers: List<MentoringInquiryAnswerInput> = listOf(
            MentoringInquiryAnswerInput(
                "Default question",
                "Default answer",
            ),
        ),
    ) = StudentInquiriesMentoringCommand(
        studentId = studentId,
        traderId = traderId,
        inquiryAnswers = inquiryAnswers,
    )
}
