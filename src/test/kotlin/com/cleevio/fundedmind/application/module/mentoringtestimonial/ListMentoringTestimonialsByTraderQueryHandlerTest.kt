package com.cleevio.fundedmind.application.module.mentoringtestimonial

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringtestimonial.query.ListMentoringTestimonialsByTraderQuery
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ListMentoringTestimonialsByTraderQueryHandlerTest(
    @Autowired private val underTest: ListMentoringTestimonialsByTraderQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return empty list when trader has no testimonials`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        // when
        val result = underTest.handle(
            ListMentoringTestimonialsByTraderQuery(
                traderId = trader.id,
            ),
        )

        // then
        result.testimonials.shouldBeEmpty()
    }

    @Test
    fun `should list testimonials with and without pictures`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        // Create testimonial without picture
        val testimonialWithoutPicture = dataHelper.getMentoringTestimonial(
            traderId = trader.id,
            firstName = "Jane",
            lastName = "Smith",
            rating = 4,
            description = "Very helpful sessions!",
        )

        // Create testimonial with picture
        val testimonialWithPicture = dataHelper.getMentoringTestimonial(
            traderId = trader.id,
            profilePictureFileId = dataHelper.getImage(
                type = FileType.MENTORING_TESTIMONIAL_PICTURE,
                originalFileUrl = "original-url",
                compressedFileUrl = "compressed-url",
                blurHash = "blur-hash",
            ).id,
            firstName = "John",
            lastName = "Doe",
            rating = 5,
            description = "Great mentoring experience!",
        )

        // Create a testimonial for another trader (should not be returned)
        dataHelper.getMentoringTestimonial(
            traderId = dataHelper.getTrader(id = 2.toUUID()).also {
                dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
            }.id,
            firstName = "Other",
            lastName = "Person",
            rating = 3,
            description = "This should not be returned",
        )

        // when
        val result = underTest.handle(
            ListMentoringTestimonialsByTraderQuery(
                traderId = trader.id,
            ),
        )

        // then
        result.testimonials shouldHaveSize 2

        // Verify testimonial without picture
        result.testimonials.find { it.id == testimonialWithoutPicture.id }!!.run {
            firstName shouldBe "Jane"
            lastName shouldBe "Smith"
            rating shouldBe 4
            description shouldBe "Very helpful sessions!"
            picture shouldBe null
        }

        // Verify testimonial with picture
        result.testimonials.find { it.id == testimonialWithPicture.id }!!.run {
            firstName shouldBe "John"
            lastName shouldBe "Doe"
            rating shouldBe 5
            description shouldBe "Great mentoring experience!"
            picture shouldNotBe null
            picture!!.run {
                imageOriginalUrl shouldBe "original-url"
                imageCompressedUrl shouldBe "compressed-url"
                imageBlurHash shouldBe "blur-hash"
            }
        }
    }
}
