package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.port.out.GetDefaultProductPricePort.ProductPrice
import com.cleevio.fundedmind.application.common.port.out.GetDefaultProductPricePort.ProductPriceMap
import com.cleevio.fundedmind.application.module.mentoring.query.TraderGetsTheirMentorDataQuery
import com.cleevio.fundedmind.domain.common.constant.MonetaryCurrency
import com.cleevio.fundedmind.domain.common.constant.TaxBehaviour
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class TraderGetsTheirMentorDataQueryHandlerTest(
    @Autowired private val underTest: TraderGetsTheirMentorDataQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get trader mentor data - verify mappings`() {
        // given
        every { stripeService.getPriceMap(listOf("prod_1", "prod_2")) } returns ProductPriceMap(
            listOf(
                ProductPrice(
                    "prod_1",
                    MoneyResult(200, MonetaryCurrency.CZK, 21.toBigDecimal(), TaxBehaviour.INCLUSIVE),
                ),
                ProductPrice(
                    "prod_2",
                    MoneyResult(100, MonetaryCurrency.CZK, 21.toBigDecimal(), TaxBehaviour.INCLUSIVE),
                ),
            ),
        )

        val trader = dataHelper.getTrader(
            id = 1.toUUID(),
            firstName = "Karel",
            lastName = "Gott",
            biography = "Ceske ESO",
            checkoutVideoUrl = "checkout-video-url",
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )

        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader.id,
            name = "1",
            stripeIdentifier = "prod_1",
            description = "1 description",
            altDescription = "1 alt description",
            sessionsCount = 5,
            validityInDays = null,
            entityModifier = { it.makeSaleable() },
        )
        dataHelper.getProduct(
            id = 2.toUUID(),
            traderId = trader.id,
            name = "2",
            stripeIdentifier = "prod_2",
            description = "2 description",
            altDescription = "2 alt description",
            sessionsCount = 10,
            validityInDays = 30,
        )
        // unsaleable product is not included in the result
        dataHelper.getProduct(id = 3.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })
        // deleted product is not included in the result
        dataHelper.getProduct(id = 4.toUUID(), traderId = trader.id, entityModifier = { it.softDelete() })

        // when
        val result = underTest.handle(
            TraderGetsTheirMentorDataQuery(
                traderId = trader.id,
            ),
        )

        // then
        result.run {
            assertSoftly {
                traderId shouldBe trader.id
                firstName shouldBe "Karel"
                lastName shouldBe "Gott"
                biography shouldBe "Ceske ESO"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                checkoutVideoUrl shouldBe "checkout-video-url"
                mentoringProducts shouldHaveSize 2
                mentoringProducts[0].run {
                    productId shouldBe 2.toUUID()
                    name shouldBe "2"
                    description shouldBe "2 description"
                    altDescription shouldBe "2 alt description"
                    sessionsCount shouldBe 10
                    validityInDays shouldBe 30
                    price.run {
                        unitAmount shouldBe 100L
                        currency shouldBe MonetaryCurrency.CZK
                        amount shouldBeEqualComparingTo 1.toBigDecimal()
                    }
                }
                mentoringProducts[1].run {
                    productId shouldBe 1.toUUID()
                    name shouldBe "1"
                    description shouldBe "1 description"
                    altDescription shouldBe "1 alt description"
                    sessionsCount shouldBe 5
                    validityInDays shouldBe null
                    price.run {
                        unitAmount shouldBe 200L
                        currency shouldBe MonetaryCurrency.CZK
                        amount shouldBeEqualComparingTo 2.toBigDecimal()
                    }
                }
            }
        }

        verify { stripeService.getPriceMap(listOf("prod_1", "prod_2")) }
    }

    @Test
    fun `should return empty list if trader has no saleable products`() {
        val trader = dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getProduct(id = 10.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })

        every { stripeService.getPriceMap(any()) } returns ProductPriceMap(emptyList())

        val result = underTest.handle(
            TraderGetsTheirMentorDataQuery(traderId = trader.id),
        )

        result.mentoringProducts shouldBe emptyList()

        verify { stripeService.getPriceMap(emptyList()) }
    }
}
