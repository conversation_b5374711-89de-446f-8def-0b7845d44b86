package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fundedmind.application.module.course.query.SearchCoursesQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.parseIntegerList
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID

class SearchCoursesQueryHandlerTest(
    @Autowired private val underTest: SearchCoursesQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should search for course - verify mappings`() {
        dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "Joe",
            lastName = "Doe",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            title = "Course",
            courseCategory = CourseCategory.TRADING_BASICS,
        ).also { course ->
            // module with 2 lessons and 1 deleted lesson
            dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id, entityModifier = { it.softDelete() })
            }
            // module with 0 lesson
            dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id)
            // module with 1 deleted lesson
            dataHelper.getCourseModule(id = 3.toUUID(), courseId = course.id).also { module3 ->
                dataHelper.getLesson(courseModuleId = module3.id, entityModifier = { it.softDelete() })
            }
        }

        val slice = underTest.handle(
            SearchCoursesQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchCoursesQuery.Filter(searchString = null),
            ),
        )

        slice.content shouldHaveSize 1
        slice.hasMore shouldBe false
        slice.lastId shouldBe 1.toUUID()
        slice.content.single().run {
            courseId shouldBe 1.toUUID()
            title shouldBe "Course"
            courseCategory shouldBe CourseCategory.TRADING_BASICS
            moduleCount shouldBe 3
            lessonCount shouldBe 2
            published shouldBe false
            traderBio.run {
                traderId shouldBe 1.toUUID()
                position shouldBe "Mentor"
                firstName shouldBe "Joe"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
            }
        }
    }

    @Test
    fun `should search courses with correctly counted modules and lessons`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getTrader(id = 2.toUUID())

        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
        ).also { course ->
            // module with 2 lessons and 1 deleted lesson
            dataHelper.getCourseModule(id = 11.toUUID(), courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id, entityModifier = { it.softDelete() })
            }
            // deleted module with 1 lesson deleted
            dataHelper.getCourseModule(
                id = 12.toUUID(),
                courseId = course.id,
                entityModifier = { it.softDelete() },
            ).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id, entityModifier = { it.softDelete() })
            }
            // module with 1 lesson
            dataHelper.getCourseModule(id = 13.toUUID(), courseId = course.id).also { module3 ->
                dataHelper.getLesson(courseModuleId = module3.id)
            }
        }

        dataHelper.getCourse(
            // no modules
            id = 2.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
        )

        dataHelper.getCourse(
            id = 3.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.EXCLUSIVE,
        ).also { course ->
            // module with 2 lessons
            dataHelper.getCourseModule(id = 31.toUUID(), courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id)
            }

            // deleted module with 1 deleted lesson
            dataHelper.getCourseModule(
                id = 32.toUUID(),
                courseId = course.id,
                entityModifier = { it.softDelete() },
            ).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id, entityModifier = { it.softDelete() })
            }
        }

        dataHelper.getCourse(
            id = 4.toUUID(),
            traderId = 2.toUUID(),
            courseCategory = CourseCategory.STRATEGY,
        ).also { course ->
            // module with 0 lesson
            dataHelper.getCourseModule(id = 41.toUUID(), courseId = course.id)
            // deleted module with 1 deleted lesson
            dataHelper.getCourseModule(
                id = 42.toUUID(),
                courseId = course.id,
                entityModifier = { it.softDelete() },
            ).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id, entityModifier = { it.softDelete() })
            }
        }

        val slice = underTest.handle(
            SearchCoursesQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchCoursesQuery.Filter(searchString = null),
            ),
        )

        slice.content shouldHaveSize 4
        slice.hasMore shouldBe false
        slice.lastId shouldBe 4.toUUID()
        slice.content.first { it.courseId == 1.toUUID() }.run {
            moduleCount shouldBe 2
            lessonCount shouldBe 3
        }
        slice.content.first { it.courseId == 2.toUUID() }.run {
            moduleCount shouldBe 0
            lessonCount shouldBe 0
        }
        slice.content.first { it.courseId == 3.toUUID() }.run {
            moduleCount shouldBe 1
            lessonCount shouldBe 2
        }
        slice.content.first { it.courseId == 4.toUUID() }.run {
            moduleCount shouldBe 1
            lessonCount shouldBe 0
        }
    }

    @Test
    fun `should search courses and return both published and hidden courses`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
        )
        dataHelper.getCourse(
            id = 2.toUUID(),
            traderId = dataHelper.getTrader(id = 2.toUUID()).id,
            entityModifier = { it.hide() },
        )

        // when
        val slice = underTest.handle(
            SearchCoursesQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchCoursesQuery.Filter(searchString = null),
            ),
        )

        slice.content.map { it.courseId } shouldContainExactlyInAnyOrder setOf(1.toUUID(), 2.toUUID())
    }

    @Test
    fun `should search courses and should filter out deleted courses`() {
        // given
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
        )
        dataHelper.getCourse(
            id = 2.toUUID(),
            traderId = dataHelper.getTrader(id = 2.toUUID()).id,
            entityModifier = { it.softDelete() },
        )

        // when
        val slice = underTest.handle(
            SearchCoursesQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchCoursesQuery.Filter(searchString = null),
            ),
        )

        // then
        slice.content.map { it.courseId } shouldBe listOf(1.toUUID())
    }

    @Test
    fun `should search courses and return only a slice of data`() {
        // given
        dataHelper.getCourse(1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)
        dataHelper.getCourse(2.toUUID(), traderId = dataHelper.getTrader(id = 2.toUUID()).id)
        dataHelper.getCourse(3.toUUID(), traderId = dataHelper.getTrader(id = 3.toUUID()).id)

        // when
        val slice = underTest.handle(
            SearchCoursesQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(
                    size = 2,
                ),
                filter = SearchCoursesQuery.Filter(searchString = null),
            ),
        )

        // then
        slice.content.size shouldBe 2
        slice.hasMore shouldBe true
        slice.lastId shouldBe 2.toUUID()
        slice.content.map { it.courseId } shouldBe listOf(1.toUUID(), 2.toUUID())
    }

    @ParameterizedTest
    @CsvSource(
        delimiter = ';',
        nullValues = ["null"],
        value = [
            "null; [1,2,3]",
            "Jan; [1,2]",
            "Ján; [1,2]",
            "Janov; [2]",
            "Skal; [1,2]",
            "Skalica; [1]",
            "Trosky; [3]",
            "GERTRUDA; [3]",
            "Jindrich; [2]",
            "NonExistent; []",
        ],
    )
    fun `should search courses by title, trader firstName, trader lastName`(
        searchString: String?,
        expectedIdsRaw: String,
    ) {
        // given
        val expectedIds: List<UUID> = expectedIdsRaw.parseIntegerList().map { it.toUUID() }
        dataHelper.getCourse(
            id = 1.toUUID(),
            title = "Skalica",
            traderId = dataHelper.getTrader(id = 1.toUUID(), firstName = "Ján", lastName = "Ptáček").id,
        )
        dataHelper.getCourse(
            id = 2.toUUID(),
            title = "Janov",
            traderId = dataHelper.getTrader(id = 2.toUUID(), firstName = "Jindřich", lastName = "Skalický").id,
        )
        dataHelper.getCourse(
            id = 3.toUUID(),
            title = "Trosky",
            traderId = dataHelper.getTrader(id = 3.toUUID(), firstName = "Gertrúda", lastName = "Pekná").id,
        )

        val slice = underTest.handle(
            SearchCoursesQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchCoursesQuery.Filter(searchString = searchString),
            ),
        )

        // then
        slice.content.map { it.courseId } shouldBe expectedIds
    }
}
