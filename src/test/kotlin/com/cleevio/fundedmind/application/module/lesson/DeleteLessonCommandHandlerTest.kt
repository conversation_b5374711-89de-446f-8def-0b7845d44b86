package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.lesson.command.DeleteLessonCommand
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotFoundException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotRelatedToCourseException
import com.cleevio.fundedmind.domain.file.AppFileRepository
import com.cleevio.fundedmind.domain.lesson.LessonRepository
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentRepository
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotFoundException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class DeleteLessonCommandHandlerTest(
    @Autowired private val underTest: DeleteLessonCommandHandler,
    @Autowired private val lessonRepository: LessonRepository,
    @Autowired private val lessonAttachmentRepository: LessonAttachmentRepository,
    @Autowired private val appFileRepository: AppFileRepository,
) : IntegrationTest() {

    @Test
    fun `should soft delete lesson but make no changes to attachments`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        val lesson = dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
        ).also { lesson ->
            dataHelper.getLessonAttachment(
                id = 11.toUUID(),
                lessonId = lesson.id,
                name = "attachment2",
                type = LessonAttachmentType.XLSX,
                entityModifier = { it.changeAttachmentDocument(dataHelper.getDocument().id) },
            )
            dataHelper.getLessonAttachment(
                id = 12.toUUID(),
                lessonId = lesson.id,
                name = "attachment1",
                type = LessonAttachmentType.PDF,
                entityModifier = { it.changeAttachmentDocument(dataHelper.getDocument().id) },
            )
        }

        // when
        underTest.handle(
            DeleteLessonCommand(
                courseId = course.id,
                courseModuleId = courseModule.id,
                lessonId = lesson.id,
            ),
        )

        // then
        lessonRepository.findByIdOrNull(lesson.id)!!.isDeleted shouldBe true
        lessonAttachmentRepository.findAll().run {
            size shouldBe 2
            first { it.id == 11.toUUID() }.run {
                attachmentDocumentFileId shouldNotBe null
                appFileRepository.findByIdOrNull(attachmentDocumentFileId!!) shouldNotBe null
            }
            first { it.id == 12.toUUID() }.run {
                attachmentDocumentFileId shouldNotBe null
                appFileRepository.findByIdOrNull(attachmentDocumentFileId!!) shouldNotBe null
            }
        }
    }

    @Test
    fun `should throw if course module does not exist`() {
        shouldThrow<CourseModuleNotFoundException> {
            underTest.handle(
                DeleteLessonCommand(
                    courseId = 1.toUUID(),
                    courseModuleId = 999.toUUID(),
                    lessonId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if course module is not related to course`() {
        // given
        val course1 = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val differentCourse = dataHelper.getCourse(id = 2.toUUID(), traderId = dataHelper.getTrader(2.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course1.id)
        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule.id)

        // when/then
        shouldThrow<CourseModuleNotRelatedToCourseException> {
            underTest.handle(
                DeleteLessonCommand(
                    courseId = differentCourse.id,
                    courseModuleId = courseModule.id,
                    lessonId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if lesson does not exist`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        // when/then
        shouldThrow<LessonNotFoundException> {
            underTest.handle(
                DeleteLessonCommand(
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                    lessonId = 999.toUUID(),
                ),
            )
        }
    }
}
