package com.cleevio.fundedmind.application.module.user.onboarding

import com.cleevio.fundedmind.DataTestHelper
import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.crm.port.out.PatchCrmCustomerRequest
import com.cleevio.fundedmind.application.module.file.finder.AppFileFinderService
import com.cleevio.fundedmind.application.module.user.onboarding.command.FinishOnboardingCommand
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import com.cleevio.fundedmind.domain.user.toQuestionnaire
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class FinishOnboardingCommandHandlerTest(
    @Autowired private val underTest: FinishOnboardingCommandHandler,
    @Autowired private val studentRepository: StudentRepository,
    @Autowired private val appFileFinderService: AppFileFinderService,
) : IntegrationTest() {

    @Test
    fun `should finish onboarding by creating student with BASECAMP tier`() {
        every { sendEmailService.sendEmailFinishOnboardingBasecamp(1.toUUID()) } just Runs

        // Create a location for the test
        val location = dataHelper.getUserLocation(
            id = 100.toUUID(),
            street = "Test Street 123",
            city = "Test City",
            postalCode = "12345",
            state = "Test Country",
        )

        val student = dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = {
                it.upgradeToBasecamp()
                it.changeProfilePicture(
                    fileId = dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                        extension = "png",
                    ).id,
                )
                it.saveUserSurvey(
                    firstName = "Karel",
                    lastName = "Gott",
                    phone = "+************",
                    biography = "Ceske ESO",
                    country = Country.CZ,
                    firstNameVocative = "Karle",
                    lastNameVocative = "Gotte",
                    locationId = location.id,
                )
                it.saveQuestionnaire(
                    questionnaire = DataTestHelper.prepareOnboardingQuestionnaireInput().toQuestionnaire(),
                )
                it.changeState(newState = OnboardingState.QUESTIONNAIRE)
            },
        ).also {
            dataHelper.getAppUser(
                id = it.id,
                userRole = UserRole.STUDENT,
                hubspotIdentifier = 1,
                email = "<EMAIL>",
                stripeIdentifier = "cus_1",
            )
        }

        every { paymentCustomerPort.findCustomerIdsByEmail(email = "<EMAIL>") } returns listOf("cus_1")
        every {
            paymentCustomerPort.updateCustomerName(
                customerId = "cus_1",
                appUserId = any(),
                fullName = any(),
            )
        } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        underTest.handle(
            FinishOnboardingCommand(
                studentId = student.id,
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.run {
            studentTier shouldBe StudentTier.BASECAMP
            gameLevel shouldBe GameLevel.ZERO
            profilePictureFileId shouldNotBe null
            appFileFinderService.getById(profilePictureFileId!!).run {
                originalFileUrl shouldBe "url"
                compressedFileUrl shouldBe "url-comp"
                blurHash shouldBe "123"
                extension shouldBe "png"
                type shouldBe FileType.STUDENT_PROFILE_PICTURE
            }
            firstName shouldBe "Karel"
            lastName shouldBe "Gott"
            phone shouldBe "+************"
            biography shouldBe "Ceske ESO"
            country shouldBe Country.CZ
            questionnaire shouldNotBe null
            locationId shouldBe 100.toUUID()
        }

        verify {
            sendEmailService.sendEmailFinishOnboardingBasecamp(student.id)
            paymentCustomerPort.findCustomerIdsByEmail(email = "<EMAIL>")
            paymentCustomerPort.updateCustomerName(
                customerId = "cus_1",
                appUserId = student.id,
                fullName = "Karel Gott",
            )
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = 1,
                    firstName = "Karel",
                    lastName = "Gott",
                    phone = "+************",
                    countryCode = Country.CZ,
                    biography = "Ceske ESO",
                    questionnaire = DataTestHelper.prepareOnboardingQuestionnaireInput().toQuestionnaire(),
                    studentTier = StudentTier.BASECAMP,
                    networkingVisibility = NetworkingVisibility.ENABLED,
                    levelVisibility = LevelVisibility.ENABLED,
                    gameLevel = GameLevel.ZERO,
                    realLocation = "Test Street 123, 12345 Test City, Test Country",
                ),
            )
        }
    }

    @Test
    fun `should finish onboarding by creating student with MASTERCLASS tier and GameLevel ONE`() {
        val student = dataHelper.getStudentForOnboarding(
            id = 2.toUUID(),
            entityModifier = {
                it.upgradeToMasterclass()
                it.saveUserSurvey(
                    firstName = "John",
                    lastName = "Doe",
                    phone = "+************",
                    biography = "Masterclass Student",
                    country = Country.CZ,
                    firstNameVocative = "Johne",
                    lastNameVocative = "Doe",
                    locationId = null,
                )
                it.saveQuestionnaire(
                    questionnaire = DataTestHelper.prepareOnboardingQuestionnaireInput().toQuestionnaire(),
                )
                it.changeState(newState = OnboardingState.QUESTIONNAIRE)
            },
        ).also {
            dataHelper.getAppUser(
                id = it.id,
                userRole = UserRole.STUDENT,
                hubspotIdentifier = 2,
                email = "<EMAIL>",
                stripeIdentifier = "cus_2",
            )
        }

        every { sendEmailService.sendEmailFinishOnboardingMasterclass(2.toUUID()) } just Runs
        every { paymentCustomerPort.findCustomerIdsByEmail(email = "<EMAIL>") } returns listOf("cus_2")
        every { paymentCustomerPort.updateCustomerName(any(), any(), any()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        underTest.handle(
            FinishOnboardingCommand(
                studentId = student.id,
            ),
        )

        studentRepository.findByIdOrNull(student.id)!!.run {
            studentTier shouldBe StudentTier.MASTERCLASS
            gameLevel shouldBe GameLevel.ONE
            profilePictureFileId shouldBe null
        }

        verify {
            sendEmailService.sendEmailFinishOnboardingMasterclass(student.id)
            paymentCustomerPort.findCustomerIdsByEmail(email = "<EMAIL>")
            paymentCustomerPort.updateCustomerName(
                customerId = "cus_2",
                appUserId = student.id,
                fullName = "John Doe",
            )
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = 2,
                    firstName = "John",
                    lastName = "Doe",
                    phone = "+************",
                    countryCode = Country.CZ,
                    biography = "Masterclass Student",
                    questionnaire = DataTestHelper.prepareOnboardingQuestionnaireInput().toQuestionnaire(),
                    studentTier = StudentTier.MASTERCLASS,
                    networkingVisibility = NetworkingVisibility.ENABLED,
                    levelVisibility = LevelVisibility.ENABLED,
                    gameLevel = GameLevel.ONE,
                ),
            )
            sendEmailService.sendEmailGameLevelGained(student.id, GameLevel.ONE)
        }
    }
}
