package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.command.PublishCourseCommand
import com.cleevio.fundedmind.domain.course.CourseRepository
import com.cleevio.fundedmind.domain.course.exception.CourseMissingPictureException
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class PublishCourseCommandHandlerTest(
    @Autowired private val underTest: PublishCourseCommandHandler,
    @Autowired private val courseRepository: CourseRepository,
) : IntegrationTest() {

    @Test
    fun `should publish course`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = {
                it.changeIntroPictureDesktop(dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
                it.changeIntroPictureMobile(dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
            },
        )

        underTest.handle(PublishCourseCommand(courseId = 1.toUUID()))

        courseRepository.findByIdOrNull(1.toUUID())!!.run {
            published shouldBe true
        }
    }

    @Test
    fun `should throw if course is missing desktop photo`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = {
                it.changeIntroPictureMobile(dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
            },
        )

        shouldThrow<CourseMissingPictureException> {
            underTest.handle(PublishCourseCommand(courseId = 1.toUUID()))
        }
    }

    @Test
    fun `should throw if course is missing mobile photo`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = {
                it.changeIntroPictureDesktop(dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
            },
        )

        shouldThrow<CourseMissingPictureException> {
            underTest.handle(PublishCourseCommand(courseId = 1.toUUID()))
        }
    }

    @Test
    fun `should throw if course is missing both photos`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
        )

        shouldThrow<CourseMissingPictureException> {
            underTest.handle(PublishCourseCommand(courseId = 1.toUUID()))
        }
    }
}
