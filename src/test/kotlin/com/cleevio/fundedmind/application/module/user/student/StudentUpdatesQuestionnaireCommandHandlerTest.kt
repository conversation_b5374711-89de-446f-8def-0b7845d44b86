package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.adapter.`in`.rest.request.QuestionnaireInput
import com.cleevio.fundedmind.application.module.crm.port.out.PatchCrmCustomerRequest
import com.cleevio.fundedmind.application.module.user.student.command.StudentUpdatesQuestionnaireCommand
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.Questionnaire
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.domain.user.student.constant.QuestionnaireQuestionType
import com.cleevio.fundedmind.domain.user.toQuestionnaire
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.time.LocalDate

class StudentUpdatesQuestionnaireCommandHandlerTest(
    @Autowired private val underTest: StudentUpdatesQuestionnaireCommandHandler,
    @Autowired private val studentRepository: StudentRepository,
) : IntegrationTest() {

    @Test
    fun `should update student questionnaire`() {
        // Create initial questionnaire
        val initialQuestionnaire = Questionnaire(
            version = LocalDate.of(2023, 1, 1),
            data = listOf(
                Questionnaire.QuestionnaireStep(
                    question = Questionnaire.QuestionnaireQuestion(
                        text = "Initial question",
                        type = QuestionnaireQuestionType.SINGLE_CHOICE,
                    ),
                    answers = listOf(
                        Questionnaire.QuestionnaireAnswer(
                            text = "Initial answer",
                            selected = true,
                        ),
                    ),
                ),
            ),
        )

        // Create a student with the initial questionnaire
        dataHelper.getAppUser(id = 1.toUUID(), hubspotIdentifier = 1L)
        dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "John",
            lastName = "Doe",
            phone = "+************",
            biography = "Test biography",
            country = Country.CZ,
            studentTier = StudentTier.BASECAMP,
            questionnaire = initialQuestionnaire,
        )

        every { hubspotService.patchCrmUser(any()) } just Runs

        // Create new questionnaire input
        val newQuestionnaireInput = QuestionnaireInput(
            version = LocalDate.of(2023, 2, 1),
            data = listOf(
                QuestionnaireInput.QuestionnaireStep(
                    question = QuestionnaireInput.QuestionnaireQuestion(
                        text = "Updated question",
                        propertyName = "question",
                        type = QuestionnaireQuestionType.MULTIPLE_CHOICE,
                    ),
                    answers = listOf(
                        QuestionnaireInput.QuestionnaireAnswer(
                            text = "Answer 1",
                            propertyName = "answer1",
                            selected = true,
                        ),
                        QuestionnaireInput.QuestionnaireAnswer(
                            text = "Answer 2",
                            propertyName = "answer2",
                            selected = false,
                        ),
                        QuestionnaireInput.QuestionnaireAnswer(
                            text = "Answer 3",
                            propertyName = "answer3",
                            selected = true,
                        ),
                    ),
                ),
            ),
        )

        // Execute the command
        underTest.handle(
            StudentUpdatesQuestionnaireCommand(
                studentId = 1.toUUID(),
                questionnaire = newQuestionnaireInput,
            ),
        )

        verify(exactly = 1) {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = 1L,
                    questionnaire = newQuestionnaireInput.toQuestionnaire(),
                ),
            )
        }

        // Verify the questionnaire was updated
        studentRepository.findByIdOrNull(1.toUUID())!!.run {
            questionnaireOrThrow.version shouldBe LocalDate.of(2023, 2, 1)
            questionnaireOrThrow.data.size shouldBe 1
            questionnaireOrThrow.data[0].question.text shouldBe "Updated question"
            questionnaireOrThrow.data[0].question.type shouldBe QuestionnaireQuestionType.MULTIPLE_CHOICE
            questionnaireOrThrow.data[0].answers.size shouldBe 3
            questionnaireOrThrow.data[0].answers[0].propertyName shouldBe "answer1"
            questionnaireOrThrow.data[0].answers[0].text shouldBe "Answer 1"
            questionnaireOrThrow.data[0].answers[0].selected shouldBe true
            questionnaireOrThrow.data[0].answers[1].propertyName shouldBe "answer2"
            questionnaireOrThrow.data[0].answers[1].text shouldBe "Answer 2"
            questionnaireOrThrow.data[0].answers[1].selected shouldBe false
            questionnaireOrThrow.data[0].answers[2].text shouldBe "Answer 3"
            questionnaireOrThrow.data[0].answers[2].propertyName shouldBe "answer3"
            questionnaireOrThrow.data[0].answers[2].selected shouldBe true
        }
    }
}
