package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.adapter.`in`.hubspot.request.HubspotChangeSource
import com.cleevio.fundedmind.application.module.user.student.command.UpdateStudentTierFromHubspotCommand
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class UpdateStudentTierFromHubspotCommandHandlerTest(
    @Autowired private val underTest: UpdateStudentTierFromHubspotCommandHandler,
    @Autowired private val studentRepository: StudentRepository,
) : IntegrationTest() {

    @Test
    fun `should update student tier successfully`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            hubspotIdentifier = 1L,
        )
        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.BASECAMP,
        )

        every { sendEmailService.sendEmailExclusiveUpgraded(1.toUUID()) } just Runs
        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs

        underTest.handle(
            UpdateStudentTierFromHubspotCommand(
                userHubspotIdentifier = 1L,
                changeSource = HubspotChangeSource.CRM_UI,
                tier = StudentTier.EXCLUSIVE,
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.studentTier shouldBe StudentTier.EXCLUSIVE

        verify {
            sendEmailService.sendEmailExclusiveUpgraded(1.toUUID())
            sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.ONE)
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = HubspotChangeSource::class,
        names = ["CRM_UI", "MOBILE_IOS"],
        mode = EnumSource.Mode.EXCLUDE,
    )
    fun `should do nothing when changeSource is not hubspot ui - parameterized test`(
        changeSource: HubspotChangeSource,
    ) {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            hubspotIdentifier = 1L,
        )
        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.BASECAMP,
        )

        underTest.handle(
            UpdateStudentTierFromHubspotCommand(
                userHubspotIdentifier = 1L,
                changeSource = changeSource,
                tier = StudentTier.EXCLUSIVE,
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.studentTier shouldBe StudentTier.BASECAMP

        verify(exactly = 0) {
            sendEmailService.sendEmailExclusiveUpgraded(1.toUUID())
        }
    }

    @Test
    fun `should do nothing when user is not a student`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            hubspotIdentifier = 1L,
            userRole = UserRole.TRADER,
        )

        underTest.handle(
            UpdateStudentTierFromHubspotCommand(
                userHubspotIdentifier = 1L,
                changeSource = HubspotChangeSource.CRM_UI,
                tier = StudentTier.EXCLUSIVE,
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID()) shouldBe null

        verify(exactly = 0) {
            sendEmailService.sendEmailExclusiveUpgraded(any())
            sendEmailService.sendEmailGameLevelGained(any(), any())
        }
    }
}
