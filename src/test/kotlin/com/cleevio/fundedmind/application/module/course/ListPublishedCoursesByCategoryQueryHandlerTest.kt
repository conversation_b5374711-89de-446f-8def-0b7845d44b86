package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.query.ListPublishedCoursesByCategoryQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class ListPublishedCoursesByCategoryQueryHandlerTest(
    @Autowired private val underTest: ListPublishedCoursesByCategoryQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list published courses - verify mappings`() {
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.MASTERCLASS, entityModifier = {
            it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
        })

        dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "Joe",
            lastName = "Doe",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )

        dataHelper.getCourse(
            id = 1.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            title = "Course",
            description = "Description",
            thumbnailUrl = "thumbnail-url",
            thumbnailAnimationUrl = "thumbnail-animation-url",
            color = Color.ORANGE,
            courseCategory = CourseCategory.TRADING_BASICS,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            // module with 2 lessons and 1 deleted lesson
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(
                    id = 101.toUUID(),
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        userId = 0.toUUID(),
                        lessonId = lesson.id,
                        entityModifier = { it.finish() },
                    )
                }
                dataHelper.getLesson(
                    id = 102.toUUID(),
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                )
                dataHelper.getLesson(
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                    entityModifier = { it.softDelete() },
                )
            }

            // module with 1 lesson that is finished
            dataHelper.getCourseModule(courseId = course.id).also { module2 ->
                dataHelper.getLesson(
                    courseModuleId = module2.id,
                    durationInSeconds = 100,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        userId = 0.toUUID(),
                        lessonId = lesson.id,
                        entityModifier = { it.finish() },
                    )
                }
            }

            // module with 1 deleted lesson that is finished
            dataHelper.getCourseModule(courseId = course.id).also { module3 ->
                dataHelper.getLesson(
                    courseModuleId = module3.id,
                    durationInSeconds = 1000,
                    entityModifier = { it.softDelete() },
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        userId = 0.toUUID(),
                        lessonId = lesson.id,
                        entityModifier = { it.finish() },
                    )
                }
            }

            // deleted module with deleted lesson
            dataHelper.getCourseModule(
                id = 4.toUUID(),
                courseId = course.id,
                entityModifier = { it.softDelete() },
            ).also { module4 ->
                dataHelper.getLesson(
                    courseModuleId = module4.id,
                    durationInSeconds = 10000,
                    entityModifier = { it.softDelete() },
                )
            }
        }

        val result = underTest.handle(
            ListPublishedCoursesByCategoryQuery(
                userId = 0.toUUID(),
                filter = ListPublishedCoursesByCategoryQuery.Filter(
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        result.data shouldHaveSize 1
        result.data.single().run {
            courseId shouldBe 1.toUUID()
            courseCategory shouldBe CourseCategory.TRADING_BASICS
            listingOrder shouldBe 1
            traderBio shouldNotBe null
            traderBio!!.run {
                traderId shouldBe 1.toUUID()
                position shouldBe "Mentor"
                firstName shouldBe "Joe"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
            }
            color shouldBe Color.ORANGE
            thumbnailUrl shouldBe "thumbnail-url"
            thumbnailAnimationUrl shouldBe "thumbnail-animation-url"
            lessonCount shouldBe 3
            totalDurationInSeconds shouldBe 120
            title shouldBe "Course"
            description shouldBe "Description"
            finishedLessons shouldBe 2
            isLockedForMe shouldBe false
        }
    }

    @Test
    fun `should list published courses - list only published courses`() {
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.BASECAMP)

        dataHelper.getTrader(id = 1.toUUID())

        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
            }

            dataHelper.getCourseModule(courseId = course.id).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id, durationInSeconds = 100)
            }
        }
        dataHelper.getCourse(
            id = 2.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
            entityModifier = { it.hide() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
            }

            dataHelper.getCourseModule(courseId = course.id).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id, durationInSeconds = 100)
            }
        }

        val result = underTest.handle(
            ListPublishedCoursesByCategoryQuery(
                userId = 0.toUUID(),
                filter = ListPublishedCoursesByCategoryQuery.Filter(
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        result.data.map { it.courseId } shouldBe listOf(1.toUUID())
        result.data.first { it.courseId == 1.toUUID() }.run {
            lessonCount shouldBe 3
            totalDurationInSeconds shouldBe 120
        }
    }

    @Test
    fun `should list published courses - filter out deleted courses and return only given category`() {
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.BASECAMP)

        dataHelper.getTrader(id = 1.toUUID())

        dataHelper.getCourse(
            id = 1.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
            traderId = 1.toUUID(),
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
            }
        }
        dataHelper.getCourse(
            id = 2.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
            traderId = 1.toUUID(),
            entityModifier = {
                it.createPicturesAndPublish()
                it.softDelete()
            },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 100)
            }
        }
        dataHelper.getCourse(
            id = 3.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
            traderId = 1.toUUID(),
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 1000)
            }
        }
        dataHelper.getCourse(
            id = 4.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
            traderId = 1.toUUID(),
            entityModifier = {
                it.createPicturesAndPublish()
                it.softDelete()
            },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10000)
            }
        }

        val result = underTest.handle(
            ListPublishedCoursesByCategoryQuery(
                userId = 0.toUUID(),
                filter = ListPublishedCoursesByCategoryQuery.Filter(
                    courseCategory = CourseCategory.BASECAMP,
                ),
            ),
        )

        result.data.map { it.courseId } shouldBe listOf(1.toUUID())
        result.data.first { it.courseId == 1.toUUID() }.run {
            lessonCount shouldBe 1
            totalDurationInSeconds shouldBe 10
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = UserRole::class,
        names = ["ADMIN", "TRADER"],
        mode = EnumSource.Mode.INCLUDE,
    )
    fun `should list published courses - admin and trader have access to all courses`(userRole: UserRole) {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = userRole)

        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(
            id = 1.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            visibleToTiers = listOf(StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        dataHelper.getCourse(
            id = 2.toUUID(),
            listingOrder = 2,
            traderId = 1.toUUID(),
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )
        dataHelper.getCourse(
            id = 3.toUUID(),
            listingOrder = 3,
            traderId = 1.toUUID(),
            visibleToTiers = listOf(StudentTier.BASECAMP),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        dataHelper.getCourse(
            id = 4.toUUID(),
            listingOrder = 4,
            traderId = 1.toUUID(),
            visibleToTiers = listOf(),
            visibleToDiscordUsers = false,
            entityModifier = { it.createPicturesAndPublish() },
        )

        // when
        val result = underTest.handle(
            ListPublishedCoursesByCategoryQuery(
                userId = 0.toUUID(),
                filter = ListPublishedCoursesByCategoryQuery.Filter(
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        result.data.map { it.courseId } shouldBe listOf(1.toUUID(), 2.toUUID(), 3.toUUID(), 4.toUUID())
        result.data.forEach { it.isLockedForMe shouldBe false }
    }

    @ParameterizedTest
    @EnumSource(
        value = StudentTier::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["MASTERCLASS", "EXCLUSIVE"],
    )
    fun `should list published courses - should lock based on student tier`(visibleToTier: StudentTier) {
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.BASECAMP)

        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            visibleToTiers = listOf(visibleToTier),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        val result = underTest.handle(
            ListPublishedCoursesByCategoryQuery(
                userId = 0.toUUID(),
                filter = ListPublishedCoursesByCategoryQuery.Filter(
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        result.data.single().isLockedForMe shouldBe true
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
