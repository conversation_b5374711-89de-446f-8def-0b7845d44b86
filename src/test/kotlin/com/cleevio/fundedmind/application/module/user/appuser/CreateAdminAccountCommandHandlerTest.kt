package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.port.out.FirebaseUserDetail
import com.cleevio.fundedmind.application.module.user.appuser.command.CreateAdminAccountCommand
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserWithEmailAlreadyExistsException
import com.cleevio.fundedmind.domain.user.appuser.AppUserRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class CreateAdminAccountCommandHandlerTest(
    @Autowired private val underTest: CreateAdminAccountCommandHandler,
    @Autowired private val appUserRepository: AppUserRepository,
) : IntegrationTest() {

    @Test
    fun `should create admin account`() {
        every {
            firebaseService.createUser(email = "<EMAIL>", rawPassword = "123456")
        } returns Result.success(
            FirebaseUserDetail(
                email = "<EMAIL>",
                firebaseIdentifier = "firebase-123456",
            ),
        )
        every {
            hubspotService.createCrmUser(
                email = "<EMAIL>",
                role = UserRole.ADMIN,
                traderReferral = null,
                phone = null,
            )
        } returns 1L

        val result = underTest.handle(
            CreateAdminAccountCommand(
                email = "<EMAIL>",
                rawPassword = "123456",
            ),
        )

        appUserRepository.findByIdOrNull(result.id)!!.run {
            firebaseIdentifier shouldBe "firebase-123456"
            email shouldBe "<EMAIL>"
            role shouldBe UserRole.ADMIN
            hubspotIdentifier shouldBe 1L
            traderReferral shouldBe null
        }

        verify {
            firebaseService.createUser(email = "<EMAIL>", rawPassword = "123456")
            hubspotService.createCrmUser(
                email = "<EMAIL>",
                role = UserRole.ADMIN,
                traderReferral = null,
                phone = null,
            )
        }
    }

    @Test
    fun `should throw if account with email already exists`() {
        dataHelper.getAppUser(1.toUUID(), email = "<EMAIL>")

        shouldThrow<UserWithEmailAlreadyExistsException> {
            underTest.handle(
                CreateAdminAccountCommand(
                    email = "<EMAIL>",
                    rawPassword = "password",
                ),
            )
        }
    }
}
