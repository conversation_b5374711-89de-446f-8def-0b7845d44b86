package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.GeoLocationInput
import com.cleevio.fundedmind.application.common.command.UserLocationInput
import com.cleevio.fundedmind.application.module.user.student.command.StudentUpdatesProfileCommand
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.location.UserLocationRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class StudentUpdatesProfileCommandHandlerTest(
    @Autowired private val underTest: StudentUpdatesProfileCommandHandler,
    @Autowired private val studentRepository: StudentRepository,
    @Autowired private val userLocationRepository: UserLocationRepository,
) : IntegrationTest() {

    @Test
    fun `should update student profile`() {
        dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "Karel",
            lastName = "Gott",
            phone = "+************",
            biography = "Ceske ESO",
            country = Country.CZ,
        ).also {
            dataHelper.getAppUser(
                id = it.id,
                userRole = UserRole.STUDENT,
                email = "<EMAIL>",
                stripeIdentifier = "cus_11",
                hubspotIdentifier = 1,
            )
        }

        every { paymentCustomerPort.findCustomerIdsByEmail("<EMAIL>") } returns listOf("cus_1")
        every { paymentCustomerPort.updateCustomerName("cus_1", 1.toUUID(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Create a location input
        val locationInput = UserLocationInput(
            street = "Test Street 123",
            city = "Test City",
            postalCode = "12345",
            state = "Test Country",
            geoLocation = GeoLocationInput(
                latitude = 49.1950610,
                longitude = 16.606836,
            ),
        )

        underTest.handle(
            StudentUpdatesProfileCommand(
                studentId = 1.toUUID(),
                firstName = "Karol",
                lastName = "Duchon",
                phone = "+************",
                biography = "Slovenske ESO",
                country = Country.SK,
                firstNameVocative = "Karle",
                lastNameVocative = "Duchoni",
                location = locationInput,
            ),
        )

        // Verify student data
        val student = studentRepository.findByIdOrNull(1.toUUID())!!
        student.run {
            firstName shouldBe "Karol"
            lastName shouldBe "Duchon"
            phone shouldBe "+************"
            biography shouldBe "Slovenske ESO"
            country shouldBe Country.SK
            firstNameVocative shouldBe "Karle"
            lastNameVocative shouldBe "Duchoni"
            locationId shouldNotBe null
        }

        // Verify location data
        userLocationRepository.findByIdOrNull(student.locationId!!)!!.run {
            street shouldBe "Test Street 123"
            city shouldBe "Test City"
            postalCode shouldBe "12345"
            state shouldBe "Test Country"
            latitude shouldBe 49.1950610
            longitude shouldBe 16.606836

            // Verify that obfuscated values are different from original values
            obfuscatedLatitude shouldNotBe latitude
            obfuscatedLongitude shouldNotBe longitude
        }

        verify {
            paymentCustomerPort.findCustomerIdsByEmail("<EMAIL>")
            paymentCustomerPort.updateCustomerName("cus_1", 1.toUUID(), "Karol Duchon")
            hubspotService.patchCrmUser(
                withArg {
                    it.firstName shouldBe "Karol"
                    it.lastName shouldBe "Duchon"
                    it.phone shouldBe "+************"
                    it.biography shouldBe "Slovenske ESO"
                    it.countryCode shouldBe Country.SK
                },
            )
        }
    }

    @Test
    fun `should update student profile from existing location to new location`() {
        // Create a location
        val existingLocation = dataHelper.getUserLocation(
            id = 1.toUUID(),
            street = "Old Street 123",
            city = "Old City",
            postalCode = "54321",
            state = "Old Country",
            latitude = 48.1950610,
            longitude = 17.606836,
        )

        // Create a student with the location
        dataHelper.getStudent(
            id = 1.toUUID(),
            locationId = existingLocation.id,
        ).also {
            dataHelper.getAppUser(
                id = it.id,
                userRole = UserRole.STUDENT,
                email = "<EMAIL>",
                stripeIdentifier = "cus_11",
                hubspotIdentifier = 1,
            )
        }

        every { paymentCustomerPort.findCustomerIdsByEmail("<EMAIL>") } returns listOf("cus_1")
        every { paymentCustomerPort.updateCustomerName("cus_1", 1.toUUID(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        // Create a new location input
        val newLocationInput = UserLocationInput(
            street = "New Street 456",
            city = "New City",
            postalCode = "12345",
            state = "New Country",
            geoLocation = GeoLocationInput(
                latitude = 49.1950610,
                longitude = 16.606836,
            ),
        )

        underTest.handle(
            StudentUpdatesProfileCommand(
                studentId = 1.toUUID(),
                firstName = "Karol",
                lastName = "Duchon",
                phone = "+************",
                biography = "Slovenske ESO",
                country = Country.SK,
                firstNameVocative = "Karle",
                lastNameVocative = "Duchoni",
                location = newLocationInput,
            ),
        )

        // Verify student data
        val student = studentRepository.findByIdOrNull(1.toUUID())!!
        student.run {
            locationId shouldNotBe null
            locationId shouldNotBe existingLocation.id
        }

        // Verify old location is deleted
        userLocationRepository.findByIdOrNull(existingLocation.id) shouldBe null

        // Verify new location data
        userLocationRepository.findByIdOrNull(student.locationId!!)!!.run {
            street shouldBe "New Street 456"
            city shouldBe "New City"
            postalCode shouldBe "12345"
            state shouldBe "New Country"
            latitude shouldBe 49.1950610
            longitude shouldBe 16.606836
        }

        verify {
            paymentCustomerPort.findCustomerIdsByEmail("<EMAIL>")
            paymentCustomerPort.updateCustomerName("cus_1", 1.toUUID(), "Karol Duchon")
            hubspotService.patchCrmUser(
                withArg {
                    it.firstName shouldBe "Karol"
                    it.lastName shouldBe "Duchon"
                    it.phone shouldBe "+************"
                    it.biography shouldBe "Slovenske ESO"
                    it.countryCode shouldBe Country.SK
                },
            )
        }
    }

    @Test
    fun `should update student profile from existing location to null location`() {
        // Create a location
        val existingLocation = dataHelper.getUserLocation(
            id = 1.toUUID(),
            street = "Old Street 123",
            city = "Old City",
            postalCode = "54321",
            state = "Old Country",
            latitude = 48.1950610,
            longitude = 17.606836,
        )

        // Create a student with the location
        dataHelper.getStudent(
            id = 1.toUUID(),
            locationId = existingLocation.id,
        ).also {
            dataHelper.getAppUser(
                id = it.id,
                userRole = UserRole.STUDENT,
                email = "<EMAIL>",
                stripeIdentifier = "cus_11",
                hubspotIdentifier = 1,
            )
        }

        every { paymentCustomerPort.findCustomerIdsByEmail("<EMAIL>") } returns listOf("cus_1")
        every { paymentCustomerPort.updateCustomerName("cus_1", 1.toUUID(), any()) } just Runs
        every { hubspotService.patchCrmUser(any()) } just Runs

        underTest.handle(
            StudentUpdatesProfileCommand(
                studentId = 1.toUUID(),
                firstName = "Karol",
                lastName = "Duchon",
                phone = "+************",
                biography = "Slovenske ESO",
                country = Country.SK,
                firstNameVocative = "Karle",
                lastNameVocative = "Duchoni",
                location = null,
            ),
        )

        // Verify student data
        studentRepository.findByIdOrNull(1.toUUID())!!.locationId shouldBe null

        // Verify old location is deleted
        userLocationRepository.findByIdOrNull(existingLocation.id) shouldBe null

        verify {
            paymentCustomerPort.findCustomerIdsByEmail("<EMAIL>")
            paymentCustomerPort.updateCustomerName("cus_1", 1.toUUID(), "Karol Duchon")
            hubspotService.patchCrmUser(
                withArg {
                    it.firstName shouldBe "Karol"
                    it.lastName shouldBe "Duchon"
                    it.phone shouldBe "+************"
                    it.biography shouldBe "Slovenske ESO"
                    it.countryCode shouldBe Country.SK
                },
            )
        }
    }
}
