package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.crm.port.out.PatchCrmCustomerRequest
import com.cleevio.fundedmind.application.module.user.student.command.StudentPatchesPrivacyCommand
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class StudentPatchesPrivacyCommandHandlerTest(
    @Autowired private val underTest: StudentPatchesPrivacyCommandHandler,
    @Autowired private val studentRepository: StudentRepository,
) : IntegrationTest() {

    @Test
    fun `should update student visibility settings`() {
        dataHelper.getStudent(
            id = 1.toUUID(),
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.ENABLED,
        ).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT)
        }

        every { hubspotService.patchCrmUser(any()) } just Runs

        underTest.handle(
            StudentPatchesPrivacyCommand(
                studentId = 1.toUUID(),
                networkingVisibility = NetworkingVisibility.DISABLED,
                levelVisibility = LevelVisibility.DISABLED,
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.run {
            networkingVisibility shouldBe NetworkingVisibility.DISABLED
            levelVisibility shouldBe LevelVisibility.DISABLED
        }

        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = 1,
                    networkingVisibility = NetworkingVisibility.DISABLED,
                    levelVisibility = LevelVisibility.DISABLED,
                ),
            )
        }
    }
}
