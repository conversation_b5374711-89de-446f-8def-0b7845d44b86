package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.adapter.`in`.hubspot.request.HubspotChangeSource
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.module.user.student.command.UpdateBanStudentFromHubspotCommand
import com.cleevio.fundedmind.domain.user.appuser.AppUserRepository
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class UpdateBanStudentFromHubspotCommandHandlerTest(
    @Autowired private val commandBus: CommandBus,
    @Autowired private val studentRepository: StudentRepository,
    @Autowired private val appUserRepository: AppUserRepository,
) : IntegrationTest() {

    @Test
    fun `should ban student successfully`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            hubspotIdentifier = 1L,
        )
        dataHelper.getStudent(
            id = 1.toUUID(),
        )

        every { firebaseService.disableUser(any()) } returns Result.success(Unit)

        commandBus.invoke(
            UpdateBanStudentFromHubspotCommand(
                userHubspotIdentifier = 1L,
                changeSource = HubspotChangeSource.CRM_UI,
                propertyValue = true,
            ),
        )

        val deletedStudent = studentRepository.getReferenceById(1.toUUID())
        deletedStudent.isDeleted shouldBe true

        val deletedAppUser = appUserRepository.getReferenceById(1.toUUID())
        deletedAppUser.accountActive shouldBe false

        verify {
            firebaseService.disableUser("<EMAIL>")
        }
    }

    @Test
    fun `should unban student successfully`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            hubspotIdentifier = 1L,
        ) { it.disableAccount() }
        dataHelper.getStudent(
            id = 1.toUUID(),
        ) {
            it.softDelete()
        }

        every { firebaseService.enableUser(any()) } returns Result.success(Unit)

        commandBus.invoke(
            UpdateBanStudentFromHubspotCommand(
                userHubspotIdentifier = 1L,
                changeSource = HubspotChangeSource.CRM_UI,
                propertyValue = false,
            ),
        )

        val updatedStudent = studentRepository.getReferenceById(1.toUUID())
        updatedStudent.isDeleted shouldBe false

        val updatedAppUser = appUserRepository.getReferenceById(1.toUUID())
        updatedAppUser.accountActive shouldBe true

        verify {
            firebaseService.enableUser("<EMAIL>")
        }
    }
}
