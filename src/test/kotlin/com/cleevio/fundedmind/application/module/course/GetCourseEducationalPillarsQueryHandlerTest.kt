package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.query.GetCourseEducationalPillarsQuery
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired

class GetCourseEducationalPillarsQueryHandlerTest(
    @Autowired private val underTest: GetCourseEducationalPillarsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get course pillars - verify mappings`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP)

        dataHelper.getTrader(id = 2.toUUID()).also { trader ->
            dataHelper.getCourse(
                id = 1.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.BASECAMP,
                entityModifier = { it.createPicturesAndPublish() },
            )
            dataHelper.getCourse(
                id = 2.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.TRADING_BASICS,
                entityModifier = { it.createPicturesAndPublish() },
            )
            dataHelper.getCourse(
                id = 3.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.STRATEGY,
                entityModifier = { it.createPicturesAndPublish() },
            )
            dataHelper.getCourse(
                id = 4.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.RECORDING,
                entityModifier = { it.createPicturesAndPublish() },
            )
            dataHelper.getCourse(
                id = 5.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.ADD_ON,
                entityModifier = { it.createPicturesAndPublish() },
            )
            dataHelper.getCourse(
                id = 6.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.EXCLUSIVE,
                entityModifier = { it.createPicturesAndPublish() },
            )
        }

        // when
        val result = underTest.handle(
            GetCourseEducationalPillarsQuery(
                userId = 1.toUUID(),
            ),
        )

        // then
        result.pillars.run {
            size shouldBe 6
            get(CourseCategory.BASECAMP)!!.run {
                courseCategory shouldBe CourseCategory.BASECAMP
                hasSingleCourse shouldBe true
                firstCourseId shouldBe 1.toUUID()
                isLockedForMe shouldBe false
            }
            get(CourseCategory.TRADING_BASICS)!!.run {
                courseCategory shouldBe CourseCategory.TRADING_BASICS
                hasSingleCourse shouldBe true
                firstCourseId shouldBe 2.toUUID()
                isLockedForMe shouldBe true
            }
            get(CourseCategory.STRATEGY)!!.run {
                courseCategory shouldBe CourseCategory.STRATEGY
                hasSingleCourse shouldBe true
                firstCourseId shouldBe 3.toUUID()
                isLockedForMe shouldBe true
            }
            get(CourseCategory.RECORDING)!!.run {
                courseCategory shouldBe CourseCategory.RECORDING
                hasSingleCourse shouldBe true
                firstCourseId shouldBe 4.toUUID()
                isLockedForMe shouldBe false // record is never locked
            }
            get(CourseCategory.ADD_ON)!!.run {
                courseCategory shouldBe CourseCategory.ADD_ON
                hasSingleCourse shouldBe true
                firstCourseId shouldBe 5.toUUID()
                isLockedForMe shouldBe true
            }
            get(CourseCategory.EXCLUSIVE)!!.run {
                courseCategory shouldBe CourseCategory.EXCLUSIVE
                hasSingleCourse shouldBe true
                firstCourseId shouldBe 6.toUUID()
                isLockedForMe shouldBe true
            }
        }
    }

    @Test
    fun `should get course pillars - unpublished course is not returned`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.MASTERCLASS)

        dataHelper.getTrader(id = 2.toUUID()).also { trader ->
            dataHelper.getCourse(id = 1.toUUID(), traderId = trader.id, courseCategory = CourseCategory.BASECAMP)
        }

        // when
        val result = underTest.handle(
            GetCourseEducationalPillarsQuery(
                userId = 1.toUUID(),
            ),
        )

        // then
        result.pillars[CourseCategory.BASECAMP]!!.run {
            courseCategory shouldBe CourseCategory.BASECAMP
            hasSingleCourse shouldBe false
            firstCourseId shouldBe null
            isLockedForMe shouldBe false
        }
    }

    @Test
    fun `should get course pillars - basecamp has 2 courses but one is deleted`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.MASTERCLASS)

        dataHelper.getTrader(id = 2.toUUID()).also { trader ->
            dataHelper.getCourse(
                id = 10.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.BASECAMP,
                entityModifier = {
                    it.createPicturesAndPublish()
                    it.softDelete()
                },
            )
            dataHelper.getCourse(
                id = 11.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.BASECAMP,
                entityModifier = { it.createPicturesAndPublish() },
            )
        }

        // when
        val result = underTest.handle(
            GetCourseEducationalPillarsQuery(
                userId = 1.toUUID(),
            ),
        )

        // then
        result.pillars[CourseCategory.BASECAMP]!!.run {
            hasSingleCourse shouldBe true
            firstCourseId shouldBe 11.toUUID()
        }
    }

    @Test
    fun `should get course pillars - trading basics has 2 courses but one is deleted`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.MASTERCLASS)

        dataHelper.getTrader(id = 2.toUUID()).also { trader ->
            dataHelper.getCourse(
                id = 10.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.TRADING_BASICS,
                entityModifier = {
                    it.createPicturesAndPublish()
                    it.softDelete()
                },
            )
            dataHelper.getCourse(
                id = 11.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.TRADING_BASICS,
                entityModifier = { it.createPicturesAndPublish() },
            )
        }

        // when
        val result = underTest.handle(
            GetCourseEducationalPillarsQuery(
                userId = 1.toUUID(),
            ),
        )

        // then
        result.pillars[CourseCategory.TRADING_BASICS]!!.run {
            hasSingleCourse shouldBe true
            firstCourseId shouldBe 11.toUUID()
        }
    }

    @Test
    fun `should get course pillars - there is no course in a category`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP)

        dataHelper.getTrader(id = 2.toUUID()).also { trader ->
            dataHelper.getCourse(
                id = 1.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.BASECAMP,
                entityModifier = { it.createPicturesAndPublish() },
            )
        }

        // when
        val result = underTest.handle(
            GetCourseEducationalPillarsQuery(
                userId = 1.toUUID(),
            ),
        )

        // then
        result.pillars.run {
            size shouldBe 6
            get(CourseCategory.BASECAMP)!!.run {
                hasSingleCourse shouldBe true
                firstCourseId shouldBe 1.toUUID()
            }
            get(CourseCategory.TRADING_BASICS)!!.run {
                hasSingleCourse shouldBe false
                firstCourseId shouldBe null
            }
            get(CourseCategory.STRATEGY)!!.run {
                hasSingleCourse shouldBe false
                firstCourseId shouldBe null
            }
            get(CourseCategory.RECORDING)!!.run {
                hasSingleCourse shouldBe false
                firstCourseId shouldBe null
            }
            get(CourseCategory.ADD_ON)!!.run {
                hasSingleCourse shouldBe false
                firstCourseId shouldBe null
            }
            get(CourseCategory.EXCLUSIVE)!!.run {
                hasSingleCourse shouldBe false
                firstCourseId shouldBe null
            }
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = UserRole::class,
        names = ["ADMIN", "TRADER"],
        mode = EnumSource.Mode.INCLUDE,
    )
    fun `should get course pillars - admin and trader have access to all courses`(userRole: UserRole) {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = userRole)

        dataHelper.getTrader(id = 2.toUUID()).also { trader ->
            dataHelper.getCourse(
                id = 1.toUUID(),
                traderId = trader.id,
                courseCategory = CourseCategory.BASECAMP,
                entityModifier = { it.createPicturesAndPublish() },
            )
        }

        // when
        val result = underTest.handle(
            GetCourseEducationalPillarsQuery(
                userId = 1.toUUID(),
            ),
        )

        result.pillars.values.map { it.isLockedForMe }.all { it shouldBe false }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
