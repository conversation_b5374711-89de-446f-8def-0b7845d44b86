package com.cleevio.fundedmind.application.module.mentoringtestimonial

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringtestimonial.command.CreateMentoringTestimonialCommand
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderNotFoundException
import com.cleevio.fundedmind.domain.mentoringtestimonial.MentoringTestimonialRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class CreateMentoringTestimonialCommandHandlerTest(
    @Autowired private val underTest: CreateMentoringTestimonialCommandHandler,
    @Autowired private val mentoringTestimonialRepository: MentoringTestimonialRepository,
) : IntegrationTest() {

    @Test
    fun `should create mentoring testimonial`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())

        // when
        val result = underTest.handle(
            CreateMentoringTestimonialCommand(
                traderId = trader.id,
                firstName = "John",
                lastName = "Doe",
                rating = 5,
                description = "Great mentoring experience!",
                pictureFileId = null,
            ),
        )

        // then
        mentoringTestimonialRepository.findByIdOrNull(result.id)!!.run {
            traderId shouldBe traderId
            firstName shouldBe "John"
            lastName shouldBe "Doe"
            rating shouldBe 5
            description shouldBe "Great mentoring experience!"
            pictureFileId shouldBe null
        }
    }

    @Test
    fun `should throw if trader does not exist`() {
        // given
        // when/then
        shouldThrow<TraderNotFoundException> {
            underTest.handle(
                CreateMentoringTestimonialCommand(
                    traderId = 1.toUUID(),
                    firstName = "John",
                    lastName = "Doe",
                    rating = 5,
                    description = "Great mentoring experience!",
                    pictureFileId = null,
                ),
            )
        }
    }
}
