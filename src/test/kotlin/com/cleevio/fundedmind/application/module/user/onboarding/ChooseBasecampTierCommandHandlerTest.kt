package com.cleevio.fundedmind.application.module.user.onboarding

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.crm.port.out.PatchCrmCustomerRequest
import com.cleevio.fundedmind.application.module.user.onboarding.command.ChooseBasecampTierCommand
import com.cleevio.fundedmind.application.module.user.student.exception.StudentAlreadyOnboardedException
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class ChooseBasecampTierCommandHandlerTest(
    @Autowired private val underTest: ChooseBasecampTierCommandHandler,
    @Autowired private val studentRepository: StudentRepository,
) : IntegrationTest() {

    @Test
    fun `should upgrade onboarding tier to BASECAMP`() {
        dataHelper.getAppUser(id = 1.toUUID(), hubspotIdentifier = 1)
        dataHelper.getStudentForOnboarding(1.toUUID())

        every { hubspotService.patchCrmUser(any()) } just Runs

        underTest.handle(ChooseBasecampTierCommand(1.toUUID()))

        studentRepository.findByIdOrNull(1.toUUID())!!.studentTier shouldBe StudentTier.BASECAMP

        verify {
            hubspotService.patchCrmUser(
                PatchCrmCustomerRequest(
                    hubspotIdentifier = 1,
                    studentTier = StudentTier.BASECAMP,
                    traderReferral = null,
                ),
            )
        }
    }

    @Test
    fun `should throw if student already onboarded`() {
        dataHelper.getAppUser(id = 1.toUUID())
        dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = { it.changeState(newState = OnboardingState.ONBOARDING_COMPLETE) },
        )

        shouldThrow<StudentAlreadyOnboardedException> {
            underTest.handle(ChooseBasecampTierCommand(1.toUUID()))
        }
    }
}
