package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.command.HideCourseCommand
import com.cleevio.fundedmind.domain.course.CourseRepository
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class HideCourseCommandHandlerTest(
    @Autowired private val underTest: HideCourseCommandHandler,
    @Autowired private val courseRepository: CourseRepository,
) : IntegrationTest() {

    @Test
    fun `should hide course that was previously published`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = {
                it.changeIntroPictureDesktop(dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
                it.changeIntroPictureMobile(dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
                it.publish()
            },
        )
        courseRepository.findByIdOrNull(1.toUUID())!!.published shouldBe true

        underTest.handle(HideCourseCommand(courseId = 1.toUUID()))

        courseRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false
    }

    @Test
    fun `hiding course that was not published should not throw`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
        )

        courseRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false

        underTest.handle(HideCourseCommand(courseId = 1.toUUID()))

        courseRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false
    }
}
